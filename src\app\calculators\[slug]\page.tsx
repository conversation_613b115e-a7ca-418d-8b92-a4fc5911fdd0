import { notFound } from "next/navigation";
import { Suspense } from "react";
import { Metadata } from "next";
import { ALL_CALCULATORS } from "@/data/calculators";
import CalculatorLayout from "@/components/calculators/CalculatorLayout";
import CalculatorDialog from "@/components/calculators/CalculatorDialog";
import CalculatorSkeleton from "@/components/calculators/CalculatorSkeleton";
import { generateRouteMetadata, isValidCalculatorRoute } from "@/lib/routing";

interface CalculatorPageProps {
  params: { slug: string };
}

// Generate static paths for all calculators - optimized for performance
export async function generateStaticParams() {
  // Only generate for calculators that exist in our data
  return ALL_CALCULATORS.map((calculator) => ({
    slug: calculator.id,
  }));
}

// Static generation with ISR for better performance
export const dynamic = 'force-static';
export const revalidate = 3600; // Revalidate every hour

// Generate metadata for SEO
export async function generateMetadata({ params }: CalculatorPageProps): Promise<Metadata> {
  // Validate route first
  if (!isValidCalculatorRoute(params.slug)) {
    return {
      title: "Calculator Not Found - ToolCrush",
      description: "The requested calculator could not be found.",
    };
  }

  // Use centralized metadata generation
  const baseMetadata = generateRouteMetadata(`/calculators/${params.slug}`);

  // Get calculator-specific data for enhanced metadata
  const calculator = ALL_CALCULATORS.find(calc => calc.id === params.slug);

  return {
    ...baseMetadata,
    // Add calculator-specific metadata
    alternates: {
      canonical: `/calculators/${params.slug}`,
    },
    robots: {
      index: true,
      follow: true,
    },
    // Add structured data for calculators
    other: {
      'calculator-category': calculator?.category || 'general',
      'calculator-popular': calculator?.popular ? 'true' : 'false',
    },
  };
}

export default function CalculatorPage({ params }: CalculatorPageProps) {
  // Validate route using centralized validation
  if (!isValidCalculatorRoute(params.slug)) {
    notFound();
  }

  const calculator = ALL_CALCULATORS.find(calc => calc.id === params.slug);

  // Double-check that we have calculator data
  if (!calculator) {
    notFound();
  }

  return (
    <CalculatorLayout
      title={calculator.title}
      description={calculator.description}
      iconName={calculator.icon}
    >
      <Suspense fallback={<CalculatorSkeleton />}>
        <CalculatorDialog calculator={calculator} isModal={false} />
      </Suspense>
    </CalculatorLayout>
  );
}
