import { Metadata } from "next";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import GenericConverter from "@/components/tools/GenericConverter";
import { toolConfigs } from "@/lib/tool-configs";
import { ALL_TOOLS } from "@/data/tools";
import ToolLayout from "@/components/tools/ToolLayout";
import ToolSkeleton from "@/components/tools/ToolSkeleton";
import { generateRouteMetadata, isValidToolRoute } from "@/lib/routing";

interface ToolPageProps {
  params: { slug: string };
}

// Generate static paths for all tools - optimized for performance
export async function generateStaticParams() {
  // Only generate for tools that exist in our data
  const toolSlugs = ALL_TOOLS.map(tool => ({ slug: tool.id }));

  // Add any additional tools from toolConfigs that might not be in ALL_TOOLS
  const configSlugs = Object.keys(toolConfigs)
    .filter(slug => !ALL_TOOLS.some(tool => tool.id === slug))
    .map(slug => ({ slug }));

  return [...toolSlugs, ...configSlugs];
}

// Static generation with ISR for better performance
export const dynamic = 'force-static';
export const revalidate = 3600; // Revalidate every hour

export async function generateMetadata({ params }: ToolPageProps): Promise<Metadata> {
  // Validate route first
  if (!isValidToolRoute(params.slug)) {
    return {
      title: "Tool Not Found - ToolCrush",
      description: "The requested tool could not be found.",
    };
  }

  // Use centralized metadata generation
  const baseMetadata = generateRouteMetadata(`/tools/${params.slug}`);

  // Get tool-specific data for enhanced metadata
  const toolData = ALL_TOOLS.find(tool => tool.id === params.slug);

  // Build other metadata object conditionally
  const otherMetadata: Record<string, string> = {
    'tool-category': toolData?.category || 'utility',
  };

  if (toolData?.inputFormat && toolData?.outputFormat) {
    otherMetadata['tool-formats'] = `${toolData.inputFormat} to ${toolData.outputFormat}`;
  }

  return {
    ...baseMetadata,
    // Add tool-specific metadata
    alternates: {
      canonical: `/tools/${params.slug}`,
    },
    robots: {
      index: true,
      follow: true,
    },
    // Add structured data for tools
    other: otherMetadata,
  };
}

export default function ToolPage({ params }: ToolPageProps) {
  // Validate route using centralized validation
  if (!isValidToolRoute(params.slug)) {
    notFound();
  }

  const toolConfig = toolConfigs[params.slug];
  const toolData = ALL_TOOLS.find(tool => tool.id === params.slug);

  // Double-check that we have at least one source of tool data
  if (!toolConfig && !toolData) {
    notFound();
  }

  // Get the icon from the tool data or fallback to icon map
  const getIconForTool = (toolName: string) => {
    // First try to get icon from ALL_TOOLS data
    if (toolData?.icon) {
      return toolData.icon;
    }

    // Fallback to icon map for legacy tools
    const iconMap: Record<string, string> = {
      "pdf-to-word": "📄",
      "pdf-to-powerpoint": "📊",
      "pdf-to-excel": "📈",
      "pdf-to-jpg": "🖼️",
      "pdf-to-pdfa": "📑",
      "word-to-pdf": "📝",
      "powerpoint-to-pdf": "🎯",
      "excel-to-pdf": "📊",
      "jpg-to-pdf": "📸",
      "html-to-pdf": "🌐",
      "merge-pdf": "🔗",
      "split-pdf": "✂️",
      "compress-pdf": "🗜️",
      "rotate-pdf": "🔄",
    };
    return iconMap[toolName] || "📁";
  };

  // Use toolConfig if available, otherwise create a basic config from toolData
  const displayConfig = toolConfig || {
    title: toolData?.title || "Tool",
    description: toolData?.description || "Professional tool for your needs",
    steps: [
      "Upload your file using the uploader below.",
      "Click the process button to start.",
      "Download your processed file when ready.",
    ],
    acceptedFileTypes: "*",
    maxFileSizeMB: 50,
    convertButtonText: "Process File",
    downloadButtonText: "Download Result",
    aboutTitle: `About ${toolData?.title || "Tool"}`,
    aboutDescription: toolData?.description || "Professional tool for your needs",
    noteDescription: "Please ensure your file meets the requirements for best results.",
    conversionType: "file-to-file" as const,
  };

  return (
    <ToolLayout
      title={displayConfig.title.replace("How to ", "")}
      description={displayConfig.description}
      icon={getIconForTool(params.slug)}
    >
      <Suspense fallback={<ToolSkeleton />}>
        <GenericConverter {...displayConfig} />
      </Suspense>
    </ToolLayout>
  );
}
