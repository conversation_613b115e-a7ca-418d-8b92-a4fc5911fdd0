import { Metadata } from "next";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import GenericConverter from "@/components/tools/GenericConverter";
import { toolConfigs } from "@/lib/tool-configs";
import { ALL_TOOLS } from "@/data/tools";
import ToolLayout from "@/components/tools/ToolLayout";
import ToolSkeleton from "@/components/tools/ToolSkeleton";

type Props = {
  params: { slug: string };
};

// Generate static paths for all tools
export async function generateStaticParams() {
  // Use both toolConfigs and ALL_TOOLS to ensure all tools are covered
  const toolConfigKeys = Object.keys(toolConfigs);
  const allToolsKeys = ALL_TOOLS.map(tool => tool.id);
  const allKeys = Array.from(new Set([...toolConfigKeys, ...allToolsKeys]));

  return allKeys.map((slug) => ({
    slug,
  }));
}

// Enable ISR with blocking fallback for better performance
export const dynamic = 'force-dynamic';
export const revalidate = false;

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const toolConfig = toolConfigs[params.slug];
  const toolData = ALL_TOOLS.find(tool => tool.id === params.slug);

  if (!toolConfig && !toolData) {
    return {
      title: "Tool Not Found - ToolCrush",
      description: "The requested tool could not be found.",
    };
  }

  const title = toolConfig?.title?.replace("How to ", "") || toolData?.title || "Tool";
  const description = toolConfig?.description || toolData?.description || "Professional tool for your needs";

  return {
    title: `${title} - ToolCrush`,
    description: description,
    keywords: `tools, ${params.slug.replace("-", " ")}, online tools, converter, ${toolData?.category || 'utility'}`,
    openGraph: {
      title: `${title} - ToolCrush`,
      description: description,
      type: 'website',
      url: `/tools/${params.slug}`,
    },
    twitter: {
      card: 'summary_large_image',
      title: `${title} - ToolCrush`,
      description: description,
    },
  };
}

export default function ToolPage({ params }: Props) {
  const toolConfig = toolConfigs[params.slug];
  const toolData = ALL_TOOLS.find(tool => tool.id === params.slug);

  if (!toolConfig && !toolData) {
    notFound();
  }

  // Get the icon from the tool data or fallback to icon map
  const getIconForTool = (toolName: string) => {
    // First try to get icon from ALL_TOOLS data
    if (toolData?.icon) {
      return toolData.icon;
    }

    // Fallback to icon map for legacy tools
    const iconMap: Record<string, string> = {
      "pdf-to-word": "📄",
      "pdf-to-powerpoint": "📊",
      "pdf-to-excel": "📈",
      "pdf-to-jpg": "🖼️",
      "pdf-to-pdfa": "📑",
      "word-to-pdf": "📝",
      "powerpoint-to-pdf": "🎯",
      "excel-to-pdf": "📊",
      "jpg-to-pdf": "📸",
      "html-to-pdf": "🌐",
      "merge-pdf": "🔗",
      "split-pdf": "✂️",
      "compress-pdf": "🗜️",
      "rotate-pdf": "🔄",
    };
    return iconMap[toolName] || "📁";
  };

  // Use toolConfig if available, otherwise create a basic config from toolData
  const displayConfig = toolConfig || {
    title: toolData?.title || "Tool",
    description: toolData?.description || "Professional tool for your needs",
    steps: [
      "Upload your file using the uploader below.",
      "Click the process button to start.",
      "Download your processed file when ready.",
    ],
    acceptedFileTypes: "*",
    maxFileSizeMB: 50,
    convertButtonText: "Process File",
    downloadButtonText: "Download Result",
    aboutTitle: `About ${toolData?.title || "Tool"}`,
    aboutDescription: toolData?.description || "Professional tool for your needs",
    noteDescription: "Please ensure your file meets the requirements for best results.",
    conversionType: "file-to-file" as const,
  };

  return (
    <ToolLayout
      title={displayConfig.title.replace("How to ", "")}
      description={displayConfig.description}
      icon={getIconForTool(params.slug)}
    >
      <Suspense fallback={<ToolSkeleton />}>
        <GenericConverter {...displayConfig} />
      </Suspense>
    </ToolLayout>
  );
}
