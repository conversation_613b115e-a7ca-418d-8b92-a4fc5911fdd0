// Centralized routing configuration for user-side routes
import { ALL_TOOLS } from '@/data/tools';
import { ALL_CALCULATORS } from '@/data/calculators';

// Route types
export interface RouteConfig {
  path: string;
  component?: string;
  title: string;
  description: string;
  keywords?: string[];
  dynamic?: boolean;
}

// Static routes configuration
export const STATIC_ROUTES: Record<string, RouteConfig> = {
  home: {
    path: '/',
    title: 'ToolCrush - All-in-one PDF & Calculator Tools',
    description: 'Free online PDF tools and calculators. Convert, merge, compress PDFs and calculate mortgages, BMI, tips and more.',
    keywords: ['PDF tools', 'calculators', 'online tools', 'free tools']
  },
  tools: {
    path: '/tools',
    title: 'PDF Tools - Convert, Merge, Compress PDFs',
    description: 'Free online PDF tools to convert, merge, compress, and edit PDF files. Fast, secure, and easy to use.',
    keywords: ['PDF tools', 'convert PDF', 'merge PDF', 'compress PDF']
  },
  calculators: {
    path: '/calculators',
    title: 'Online Calculators - Financial, Math, Health & More',
    description: 'Free online calculators for finance, math, health, and conversions. Calculate mortgages, BMI, tips, and more.',
    keywords: ['calculators', 'financial calculator', 'math calculator', 'health calculator']
  },
  calculatorsAll: {
    path: '/calculators/all',
    title: 'All Calculators - Complete Collection',
    description: 'Browse our complete collection of online calculators organized by category.',
    keywords: ['all calculators', 'calculator collection', 'online calculators']
  },
  blog: {
    path: '/blog',
    title: 'Blog - Tips, Tutorials & Guides',
    description: 'Learn tips, tutorials, and best practices for PDF management and using online tools.',
    keywords: ['blog', 'tutorials', 'PDF tips', 'guides']
  },
  blogAll: {
    path: '/blog/all',
    title: 'All Articles - Blog',
    description: 'Browse all our articles, tutorials, and guides.',
    keywords: ['all articles', 'blog posts', 'tutorials']
  },
  login: {
    path: '/login',
    title: 'Login - ToolCrush',
    description: 'Login to your ToolCrush account to access premium features.',
    keywords: ['login', 'sign in', 'account']
  },
  register: {
    path: '/register',
    title: 'Sign Up - ToolCrush',
    description: 'Create a free ToolCrush account to access all features.',
    keywords: ['sign up', 'register', 'create account']
  }
};

// Dynamic route generators
export const generateToolRoutes = (): RouteConfig[] => {
  return ALL_TOOLS.map(tool => ({
    path: `/tools/${tool.id}`,
    title: `${tool.title} - Free Online Tool`,
    description: tool.description,
    keywords: [tool.title.toLowerCase(), tool.category, 'online tool', 'free'],
    dynamic: true
  }));
};

export const generateCalculatorRoutes = (): RouteConfig[] => {
  return ALL_CALCULATORS.map(calculator => ({
    path: `/calculators/${calculator.id}`,
    title: `${calculator.title} - Free Online Calculator`,
    description: calculator.description,
    keywords: [calculator.title.toLowerCase(), calculator.category, 'calculator', 'free'],
    dynamic: true
  }));
};

// Route helpers
export const getToolRoute = (toolId: string): string => `/tools/${toolId}`;
export const getCalculatorRoute = (calculatorId: string): string => `/calculators/${calculatorId}`;
export const getBlogRoute = (slug: string): string => `/blog/${slug}`;

// Navigation helpers
export const isActiveRoute = (currentPath: string, targetPath: string): boolean => {
  if (targetPath === '/') {
    return currentPath === '/';
  }
  return currentPath.startsWith(targetPath);
};

// Route validation
export const isValidToolRoute = (slug: string): boolean => {
  return ALL_TOOLS.some(tool => tool.id === slug);
};

export const isValidCalculatorRoute = (slug: string): boolean => {
  return ALL_CALCULATORS.some(calculator => calculator.id === slug);
};

// Get all user-side routes (excluding admin)
export const getAllUserRoutes = (): RouteConfig[] => {
  const staticRoutes = Object.values(STATIC_ROUTES);
  const toolRoutes = generateToolRoutes();
  const calculatorRoutes = generateCalculatorRoutes();
  
  return [...staticRoutes, ...toolRoutes, ...calculatorRoutes];
};

// Route metadata generator
export const generateRouteMetadata = (path: string) => {
  const allRoutes = getAllUserRoutes();
  const route = allRoutes.find(r => r.path === path);
  
  if (!route) {
    return {
      title: 'Page Not Found - ToolCrush',
      description: 'The page you are looking for could not be found.'
    };
  }
  
  return {
    title: route.title,
    description: route.description,
    keywords: route.keywords?.join(', '),
    openGraph: {
      title: route.title,
      description: route.description,
      type: 'website',
      url: route.path,
    },
    twitter: {
      card: 'summary_large_image',
      title: route.title,
      description: route.description,
    },
  };
};
